# Windows安装指南

本指南将帮助您在Windows系统上安装和运行DevOps运维平台。

## 系统要求

- Windows 10/11 或 Windows Server 2016+
- Python 3.9+
- MySQL 8.0+ 或 MariaDB 10.5+
- Redis 6.0+
- Git

**注意：** Windows版本已移除Ansible依赖，使用自定义的Windows部署API替代。

## 安装步骤

### 1. 安装Python

从 [Python官网](https://www.python.org/downloads/) 下载并安装Python 3.9+

**重要：** 安装时勾选"Add Python to PATH"

### 2. 安装MySQL

从 [MySQL官网](https://dev.mysql.com/downloads/mysql/) 下载并安装MySQL

或者使用XAMPP/WAMP等集成环境

### 3. 安装Redis

#### 方法1：使用WSL2（推荐）
```bash
wsl --install
# 在WSL中安装Redis
sudo apt update
sudo apt install redis-server
```

#### 方法2：使用Windows版Redis
从 [Redis Windows版](https://github.com/tporadowski/redis/releases) 下载安装

### 4. 克隆项目

```cmd
git clone <repository-url>
cd dodo-ops-main
```

### 5. 配置环境

#### 创建虚拟环境
```cmd
python -m venv venv
venv\Scripts\activate
```

#### 安装依赖
```cmd
pip install -r requirements.txt
```

#### 配置文件
```cmd
copy config_windows.py.sample config.py
```

编辑 `config.py` 文件，修改数据库和Redis连接信息。

### 6. 数据库设置

#### 创建数据库
```sql
CREATE DATABASE ydevopsdb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'devops'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON ydevopsdb.* TO 'devops'@'localhost';
FLUSH PRIVILEGES;
```

#### 运行迁移
```cmd
python manage.py makemigrations
python manage.py migrate
python manage.py initdata --type all
```

#### 创建超级用户
```cmd
python manage.py createsuperuser
```

### 7. 启动服务

#### 开发环境
```cmd
# 启动主服务
start_windows.bat

# 或者手动启动
python manage.py runserver 0.0.0.0:8080
```

#### 启动Celery（新命令行窗口）
```cmd
celery_windows.bat
```

#### 启动Django-Q（新命令行窗口）
```cmd
venv\Scripts\activate
python manage.py qcluster
```

### 8. 生产环境部署

#### 使用Windows服务
```powershell
# 以管理员身份运行PowerShell
.\install_windows_service.ps1
```

#### 使用IIS（可选）
1. 安装IIS和Python支持
2. 配置FastCGI
3. 部署Django应用

## 常见问题

### 1. PyMySQL连接问题
如果遇到MySQL连接问题，确保：
- MySQL服务正在运行
- 用户权限正确
- 防火墙允许连接

### 2. Celery在Windows上的问题
Windows下Celery需要特殊配置：
```python
# 在celery配置中添加
CELERY_TASK_ALWAYS_EAGER = True  # 开发环境
```

### 3. 路径问题
确保所有路径使用正斜杠或使用`os.path.join()`

### 4. 权限问题
某些操作可能需要管理员权限，特别是：
- 安装Windows服务
- 修改系统配置
- 访问某些系统目录

## 性能优化

### 1. 数据库优化
- 配置MySQL的`my.ini`文件
- 增加缓冲池大小
- 启用查询缓存

### 2. Redis优化
- 配置持久化
- 调整内存使用
- 启用压缩

### 3. Python优化
- 使用生产级WSGI服务器（如Waitress）
- 配置静态文件服务
- 启用缓存

## 监控和日志

### 1. 日志配置
日志文件位置：`logs/` 目录

### 2. 性能监控
- 使用Windows性能监视器
- 配置应用程序日志
- 监控数据库性能

## 备份策略

### 1. 数据库备份
```cmd
mysqldump -u devops -p ydevopsdb > backup.sql
```

### 2. 文件备份
- 备份`media/`目录
- 备份配置文件
- 备份日志文件

## 故障排除

### 1. 检查服务状态
```cmd
# 检查Python进程
tasklist | findstr python

# 检查端口占用
netstat -an | findstr :8080
```

### 2. 查看日志
检查`logs/`目录下的日志文件

### 3. 重启服务
```cmd
# 停止所有相关进程
taskkill /f /im python.exe

# 重新启动
start_windows.bat
```

## 支持

如果遇到问题，请：
1. 检查日志文件
2. 查看GitHub Issues
3. 联系技术支持
