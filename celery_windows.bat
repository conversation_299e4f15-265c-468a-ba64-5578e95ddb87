@echo off
REM Windows Celery启动脚本 - DevOps运维平台
REM 作者: <PERSON> Lai
REM 时间: 2025-01-01

echo ========================================
echo    Celery Worker - Windows启动脚本
echo ========================================

REM 检查虚拟环境
if not exist "venv\Scripts\activate.bat" (
    echo 错误: 虚拟环境不存在，请先运行start_windows.bat
    pause
    exit /b 1
)

REM 激活虚拟环境
echo 激活虚拟环境...
call venv\Scripts\activate.bat

REM 设置环境变量
set PYTHONOPTIMIZE=1

REM 获取队列名称参数
set QUEUE_NAME=celery
if not "%1"=="" set QUEUE_NAME=%1

echo 当前队列: %QUEUE_NAME%

REM 检查Redis连接
echo 检查Redis连接...
python -c "import redis; r=redis.Redis(host='127.0.0.1', port=6379, db=0); r.ping(); print('Redis连接正常')" 2>nul
if errorlevel 1 (
    echo 警告: Redis连接失败，请确认Redis服务已启动
    echo 可以从以下地址下载Redis for Windows:
    echo https://github.com/microsoftarchive/redis/releases
    pause
)

REM 创建日志目录
if not exist "logs" mkdir logs

echo ========================================
echo 启动Celery Worker...
echo 队列: %QUEUE_NAME%
echo 按 Ctrl+C 停止服务
echo ========================================

REM Windows下使用solo池启动Celery
celery -A celery_tasks worker --loglevel=info --pool=solo --without-gossip --without-mingle --without-heartbeat -E -Q %QUEUE_NAME%

pause
