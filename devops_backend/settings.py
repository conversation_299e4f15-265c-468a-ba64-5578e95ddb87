"""
Django settings for devops_backend project.

Generated by 'django-admin startproject' using Django 3.1.1.

For more information on this file, see
https://docs.djangoproject.com/en/3.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/3.1/ref/settings/
"""

import os
import sys
import re
from pathlib import Path
from datetime import timedelta
from elasticsearch_dsl import connections

import psutil

from config import ELAST<PERSON>SEARCH, MEDIA_ROOT, Q_CLUSTER_CONFIG, SECRET_KEY, DEBUG, ALLOWED_HOSTS, DATABASES_CONFIG, LOG_CONFIG, TOKEN_TIME, \
    CACHE_CONFIG, CHANNEL_CONFIG
from ansible import constants as C

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent
sys.path.insert(0, os.path.join(BASE_DIR, 'apps'))

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.1/howto/deployment/checklist/

# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'drf_yasg',
    'corsheaders',
    'rest_framework',
    'django_q',
    'channels',
    'dbapp.apps.DbappConfig',
    'ucenter.apps.UcenterConfig',
    'cmdb.apps.CmdbConfig',
    'deploy.apps.DeployConfig',
    'dashboard.apps.DashboardConfig',
    'workflow.apps.WorkflowConfig',
    'workflow_callback.apps.WorkflowCallbackConfig',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    # 工单回调中间件
    'workflow_callback.middleware.WorkflowCallbackMiddleware',
]

ROOT_URLCONF = 'devops_backend.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'templates')],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages'
            ],
        },
    },
]

WSGI_APPLICATION = 'devops_backend.wsgi.application'

ASGI_APPLICATION = 'devops_backend.routing.application'

CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels_redis.core.RedisChannelLayer',
        'CONFIG': {
            "hosts": [
                f"redis://:{CHANNEL_CONFIG.get('password', '')}@{CHANNEL_CONFIG['host']}:{CHANNEL_CONFIG['port']}/{CHANNEL_CONFIG['db']}"],
            "expiry": 30,
            "channel_capacity": {
                "http.request": 2000,
                re.compile(r"^websocket.send\!.+"): 2000,
            },
            "capacity": 2000
        },
    },
}

if CACHE_CONFIG.get('startup_nodes'):
    CACHES = {
        'default': {
            'BACKEND': 'django_redis.cache.RedisCache',
            'LOCATION': 'redis://localhost:6379',
            'TIMEOUT': 60 * 60,
            'KEY_PREFIX': CACHE_CONFIG.get('KEY_PREFIX', ''),
            'OPTIONS': {
                'CLIENT_CLASS': 'common.utils.RedisAPI.CustomRedisCluster',
                "PICKLE_VERSION": -1,
            }
        }
    }
else:
    CACHES = {
        "default": {
            "BACKEND": "django_redis.cache.RedisCache",
            "LOCATION": f"redis://{CACHE_CONFIG['host']}:{CACHE_CONFIG['port']}/{CACHE_CONFIG['db']}",
            'KEY_PREFIX': CACHE_CONFIG.get('KEY_PREFIX', ''),
            "OPTIONS": {
                "CLIENT_CLASS": "django_redis.client.DefaultClient",
                "PASSWORD": CACHE_CONFIG['password'],
            }
        }
    }

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': DATABASES_CONFIG['NAME'],
        'HOST': DATABASES_CONFIG['HOST'],
        'PORT': DATABASES_CONFIG.get('PORT', 3306),
        'USER': DATABASES_CONFIG['USER'],
        'PASSWORD': DATABASES_CONFIG['PASSWORD'],
        'OPTIONS': {
            'charset': 'utf8mb4',
            'auth_plugin': 'mysql_native_password'
        }
    }
}

Q_CLUSTER_WORKERS = Q_CLUSTER_CONFIG.get('workers', None)
# django-q配置
Q_CLUSTER = {
    'name': 'ydevops',
    'workers': Q_CLUSTER_WORKERS,
    'recycle': Q_CLUSTER_CONFIG.get('recycle', 500),
    'timeout': Q_CLUSTER_CONFIG.get('timeout', 600),
    'retry': Q_CLUSTER_CONFIG.get('retry', 660),
    'compress': True,
    'cpu_affinity': 1,
    'save_limit': Q_CLUSTER_CONFIG.get('save_limit', 0),
    'queue_limit': Q_CLUSTER_CONFIG.get('queue_limit', 100),
    'label': 'Django Q',
    'django_redis': 'default',
    'broker_class': 'common.extends.q_redis_broker.Redis',
    'sync': Q_CLUSTER_CONFIG.get('sync', False),  # 本地调试可以修改为True，使用同步模式
}

DEFAULT_AUTO_FIELD = 'django.db.models.AutoField'
DATE_FORMAT = '%Y-%m-%d'
DATETIME_FORMAT = f'{DATE_FORMAT} %H:%M:%S'

# Password validation
# https://docs.djangoproject.com/en/3.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
# https://docs.djangoproject.com/en/3.1/topics/i18n/

LANGUAGE_CODE = 'zh-Hans'

TIME_ZONE = 'Asia/Shanghai'

USE_I18N = True

USE_L10N = True

USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.1/howto/static-files/

STATIC_ROOT = os.path.join(BASE_DIR, 'static/')
STATIC_URL = '/static/'

# 跨域配置
if DEBUG:
    CORS_ALLOW_CREDENTIALS = True
    CORS_ORIGIN_ALLOW_ALL = True
    CORS_ORIGIN_WHITELIST = (
        'http://localhost:9527',
    )

    CORS_ALLOW_METHODS = (
        'DELETE',
        'GET',
        'OPTIONS',
        'PATCH',
        'POST',
        'PUT',
        'VIEW',
    )

    CORS_ALLOW_HEADERS = (
        'XMLHttpRequest',
        'X_FILENAME',
        'accept-encoding',
        'authorization',
        'content-type',
        'dnt',
        'origin',
        'user-agent',
        'x-csrftoken',
        'x-requested-with',
        'Pragma',
        'Access-Control-Allow-Origin',
        'Access-Control-Request-Headers',
        'Access-Control-Request-Method'
    )

REST_FRAMEWORK = {
    # 自定义分页
    'DEFAULT_PAGINATION_CLASS': 'common.extends.pagination.CustomPagination',
    'PAGE_SIZE': 20,
    # 用户登陆认证方式
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'common.extends.JwtAuth.JWTAuthentication',
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework.authentication.BasicAuthentication',
    ),
    # 全局权限拦截
    'DEFAULT_PERMISSION_CLASSES': (
        'common.extends.permissions.RbacPermission',
    ),
    'DEFAULT_SCHEMA_CLASS': 'rest_framework.schemas.coreapi.AutoSchema',
    'DEFAULT_FILTER_BACKENDS': (
        'django_filters.rest_framework.DjangoFilterBackend',
        'rest_framework.filters.SearchFilter',
        'rest_framework.filters.OrderingFilter',
    ),
    'DEFAULT_RENDERER_CLASSES': ['rest_framework.renderers.JSONRenderer',
                                 'rest_framework.renderers.BrowsableAPIRenderer'] if DEBUG else [
        'rest_framework.renderers.JSONRenderer']
}

# 用户模型
AUTH_USER_MODEL = 'dbapp.UserProfile'

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

AUTHENTICATION_BACKENDS = (
    'django.contrib.auth.backends.ModelBackend',
)

# JWT配置
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(**TOKEN_TIME['ACCESS']),
    'REFRESH_TOKEN_LIFETIME': timedelta(**TOKEN_TIME['REFRESH']),
    'ROTATE_REFRESH_TOKENS': False,
    'ALGORITHM': 'HS256',
    'SIGNING_KEY': SECRET_KEY,
    'AUTH_HEADER_TYPES': ('Bearer',),
    'USER_ID_FIELD': 'id',
    'USER_ID_CLAIM': 'user_id'
}

# Define a default Elasticsearch client
try:
    connections.create_connection(alias="default",
                                  hosts=ELASTICSEARCH['host'],
                                  sniff_on_start=True,
                                  # # refresh nodes after a node fails to respond
                                  sniff_on_connection_fail=True,
                                  # # and also every 30 seconds
                                  sniffer_timeout=30,
                                  use_ssl=ELASTICSEARCH['ssl'],
                                  http_auth=(
                                      ELASTICSEARCH['username'], ELASTICSEARCH['password']),
                                  timeout=ELASTICSEARCH['timeout']
                                  )
except BaseException as e:
    pass

EMAIL_BACKEND = 'common.MailSend.EmailBackend'

# elasticsearch查询日志下载目录
ESLOG_DOWNLOAD = os.path.join(MEDIA_ROOT, 'download')
if not os.path.exists(ESLOG_DOWNLOAD):
    os.makedirs(ESLOG_DOWNLOAD)

# 日志配置
BASE_LOG_DIR = os.path.join(BASE_DIR, 'logs')
if not os.path.exists(BASE_LOG_DIR):
    os.makedirs(BASE_LOG_DIR)

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,  # 是否禁用已经存在的日志器
    'formatters': {  # 日志信息显示的格式
        'verbose': {
            'format': '%(levelname)s %(name)s %(asctime)s %(module)s %(lineno)d %(message)s'
        },
        'simple': {
            'format': '%(levelname)s %(name)s %(module)s %(lineno)d %(message)s'
        },
    },
    'filters': {  # 对日志进行过滤
        'require_debug_true': {  # django在debug模式下才输出日志
            '()': 'django.utils.log.RequireDebugTrue',
        },
    },
    'handlers': {  # 日志处理方法
        'console': {  # 向终端中输出日志
            'level': 'DEBUG' if DEBUG else LOG_CONFIG['level'],
            'class': 'logging.StreamHandler',
            'formatter': 'verbose'
        },
        'file': {  # 向文件中输出日志
            'level': 'DEBUG' if DEBUG else LOG_CONFIG['level'],
            'class': 'logging.handlers.RotatingFileHandler',
            # 日志文件的位置
            'filename': os.path.join(BASE_LOG_DIR, "application.log"),
            'maxBytes': LOG_CONFIG['size'],
            'backupCount': LOG_CONFIG['backup'],
            'formatter': 'verbose'
        },
        'security': {
            'level': 'DEBUG',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, 'logs/security.log'),
            'maxBytes': 1024 * 1024 * 100,
            'backupCount': 7,
            'formatter': 'verbose',
        },
        'exec_sql': {
            'level': 'DEBUG',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, 'logs/sql_exec.log'),
            'maxBytes': 1024 * 1024 * 100,
            'backupCount': 7,
            'formatter': 'verbose',
        },
        # 取消日志中间件
        'restful_api': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, 'logs/api.log'),
            'maxBytes': 1024 * 1024 * 100,
            'backupCount': 7,
            'formatter': 'verbose',
        },
        'task_log': {
            'level': 'DEBUG' if DEBUG else LOG_CONFIG['level'],
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, 'logs/task.log'),
            'maxBytes': 1024 * 1024 * 100,
            'backupCount': 7,
            'formatter': 'verbose',
        },
        'cmdb_es': {
            'level': 'DEBUG' if DEBUG else LOG_CONFIG['level'],
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, 'logs/cmdb_es.log'),
            'maxBytes': 1024 * 1024 * 100,
            'backupCount': 7,
            'formatter': 'verbose',
        },
        'elasticsearch': {
            'level': 'DEBUG' if DEBUG else LOG_CONFIG['level'],
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, 'logs/elasticsearch.log'),
            'maxBytes': 1024 * 1024 * 100,
            'backupCount': 7,
            'formatter': 'verbose',
        },
        'error': {
            'level': 'ERROR',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, 'logs/error.log'),
            'maxBytes': 1024 * 1024 * 100,
            'backupCount': 7,
            'formatter': 'verbose',
        },
        'request_handler': {
            'level': 'DEBUG' if DEBUG else LOG_CONFIG['level'],
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, 'logs/exception.log'),
            'maxBytes': 1024 * 1024 * 100,
            'backupCount': 7,
            'formatter': 'verbose',
        },
        'django-q': {
            'level': 'DEBUG',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'logs/qcluster.log',
            'maxBytes': 1024 * 1024 * 100,  # 5 MB
            'backupCount': 5,
            'formatter': 'verbose',
        },
    },
    'loggers': {  # 日志器
        'django': {
            'handlers': ['error', 'console'],
            'level': 'DEBUG' if DEBUG else LOG_CONFIG['level'],
            'propagate': False,
        },
        'django.utils.autoreload': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
        'django.security': {
            'handlers': ['security'],
            'level': 'DEBUG' if DEBUG else LOG_CONFIG['level'],
            'propagate': True,
        },
        'django.db.backends': {
            'handlers': ['exec_sql'],
            'level': 'DEBUG' if DEBUG else LOG_CONFIG['level'],
            'propagate': False,
        },
        'rediscluster': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False
        },
        'pyexcel': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False
        },
        # 取消日志中间件
        'api': {
            'handlers': ['restful_api', 'console'],
            'level': 'DEBUG' if DEBUG else LOG_CONFIG['level'],
            'propagate': False
        },
        # 取消reload提示
        'parso': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False
        },
        # 取消pyexcel_io提示
        'pyexcel_io': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False
        },
        # 取消 lml 提示
        'lml': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False
        },
        # 取消kubernetes提示
        'kubernetes': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False
        },
        'drf': {
            'handlers': ['file', 'console'],
            'level': 'DEBUG' if DEBUG else LOG_CONFIG['level'],
            'propagate': True
        },
        'cmdb_es': {
            'handlers': ['cmdb_es'],
            'level': 'DEBUG' if DEBUG else LOG_CONFIG['level'],
            'propagate': True
        },
        'elasticsearch': {
            'handlers': ['elasticsearch'],
            'level': 'DEBUG' if DEBUG else LOG_CONFIG['level'],
            'propagate': True
        },
        'celery_tasks.tasks': {
            'handlers': ['task_log', 'console'],
            'level': 'DEBUG' if DEBUG else LOG_CONFIG['level'],
            'propagate': False
        },
        'django-q': {  # django_q模块相关日志
            'handlers': ['console', 'django-q'],
            'level': 'DEBUG' if DEBUG else LOG_CONFIG['level'],
            'propagate': False,
        }
    }
}

# 延长进程间轮询的时间， 降低负载， 防止跑 playbook 出现下面异常
# ansible.errors.AnsibleError: A worker was found in a dead state
C.DEFAULT_INTERNAL_POLL_INTERVAL = 0.1
