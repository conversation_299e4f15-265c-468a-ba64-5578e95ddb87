# Windows服务安装脚本 - DevOps运维平台
# 作者: <PERSON>
# 时间: 2025-01-01
# 说明: 以管理员身份运行此脚本来安装Windows服务

param(
    [Parameter(Mandatory=$false)]
    [string]$Action = "install",
    
    [Parameter(Mandatory=$false)]
    [string]$ServiceName = "YDevOpsService",
    
    [Parameter(Mandatory=$false)]
    [string]$DisplayName = "Y DevOps Platform Service",
    
    [Parameter(Mandatory=$false)]
    [int]$Port = 8080
)

# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "错误: 此脚本需要管理员权限运行" -ForegroundColor Red
    Write-Host "请右键点击PowerShell并选择'以管理员身份运行'" -ForegroundColor Yellow
    Read-Host "按任意键退出"
    exit 1
}

$CurrentPath = Get-Location
$PythonExe = Join-Path $CurrentPath "venv\Scripts\python.exe"
$ManagePy = Join-Path $CurrentPath "manage.py"

Write-Host "========================================" -ForegroundColor Green
Write-Host "  DevOps运维平台 - Windows服务管理" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# 检查Python虚拟环境
if (-not (Test-Path $PythonExe)) {
    Write-Host "错误: Python虚拟环境不存在" -ForegroundColor Red
    Write-Host "请先运行 start_windows.bat 创建虚拟环境" -ForegroundColor Yellow
    Read-Host "按任意键退出"
    exit 1
}

# 检查manage.py
if (-not (Test-Path $ManagePy)) {
    Write-Host "错误: manage.py文件不存在" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

function Install-Service {
    Write-Host "安装Windows服务..." -ForegroundColor Yellow
    
    # 检查服务是否已存在
    $existingService = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
    if ($existingService) {
        Write-Host "服务 $ServiceName 已存在，正在删除..." -ForegroundColor Yellow
        Stop-Service -Name $ServiceName -Force -ErrorAction SilentlyContinue
        & sc.exe delete $ServiceName
        Start-Sleep -Seconds 2
    }
    
    # 创建服务启动脚本
    $ServiceScript = @"
@echo off
cd /d "$CurrentPath"
call venv\Scripts\activate.bat
python manage.py runserver 0.0.0.0:$Port
"@
    
    $ServiceScriptPath = Join-Path $CurrentPath "service_start.bat"
    $ServiceScript | Out-File -FilePath $ServiceScriptPath -Encoding ASCII
    
    # 使用NSSM安装服务（如果可用）
    $nssmPath = Get-Command nssm -ErrorAction SilentlyContinue
    if ($nssmPath) {
        Write-Host "使用NSSM安装服务..." -ForegroundColor Green
        & nssm install $ServiceName $ServiceScriptPath
        & nssm set $ServiceName DisplayName $DisplayName
        & nssm set $ServiceName Description "DevOps运维平台Windows服务"
        & nssm set $ServiceName Start SERVICE_AUTO_START
        & nssm set $ServiceName AppDirectory $CurrentPath
    } else {
        # 使用sc命令安装服务
        Write-Host "使用SC命令安装服务..." -ForegroundColor Green
        & sc.exe create $ServiceName binPath= $ServiceScriptPath DisplayName= $DisplayName start= auto
    }
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "服务安装成功!" -ForegroundColor Green
        Write-Host "服务名称: $ServiceName" -ForegroundColor Cyan
        Write-Host "显示名称: $DisplayName" -ForegroundColor Cyan
        Write-Host "端口: $Port" -ForegroundColor Cyan
    } else {
        Write-Host "服务安装失败!" -ForegroundColor Red
    }
}

function Remove-Service {
    Write-Host "删除Windows服务..." -ForegroundColor Yellow
    
    $existingService = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
    if ($existingService) {
        Stop-Service -Name $ServiceName -Force -ErrorAction SilentlyContinue
        & sc.exe delete $ServiceName
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "服务删除成功!" -ForegroundColor Green
        } else {
            Write-Host "服务删除失败!" -ForegroundColor Red
        }
    } else {
        Write-Host "服务不存在" -ForegroundColor Yellow
    }
    
    # 删除服务脚本
    $ServiceScriptPath = Join-Path $CurrentPath "service_start.bat"
    if (Test-Path $ServiceScriptPath) {
        Remove-Item $ServiceScriptPath -Force
    }
}

function Start-DevOpsService {
    Write-Host "启动服务..." -ForegroundColor Yellow
    Start-Service -Name $ServiceName
    
    if ($?) {
        Write-Host "服务启动成功!" -ForegroundColor Green
        Write-Host "访问地址: http://localhost:$Port" -ForegroundColor Cyan
    } else {
        Write-Host "服务启动失败!" -ForegroundColor Red
    }
}

function Stop-DevOpsService {
    Write-Host "停止服务..." -ForegroundColor Yellow
    Stop-Service -Name $ServiceName -Force
    
    if ($?) {
        Write-Host "服务停止成功!" -ForegroundColor Green
    } else {
        Write-Host "服务停止失败!" -ForegroundColor Red
    }
}

function Show-ServiceStatus {
    $service = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
    if ($service) {
        Write-Host "服务状态:" -ForegroundColor Cyan
        Write-Host "  名称: $($service.Name)" -ForegroundColor White
        Write-Host "  显示名称: $($service.DisplayName)" -ForegroundColor White
        Write-Host "  状态: $($service.Status)" -ForegroundColor White
        Write-Host "  启动类型: $($service.StartType)" -ForegroundColor White
    } else {
        Write-Host "服务不存在" -ForegroundColor Yellow
    }
}

# 主逻辑
switch ($Action.ToLower()) {
    "install" { Install-Service }
    "remove" { Remove-Service }
    "uninstall" { Remove-Service }
    "start" { Start-DevOpsService }
    "stop" { Stop-DevOpsService }
    "status" { Show-ServiceStatus }
    default {
        Write-Host "用法: .\install_windows_service.ps1 -Action [install|remove|start|stop|status]" -ForegroundColor Yellow
        Write-Host "示例:" -ForegroundColor Cyan
        Write-Host "  .\install_windows_service.ps1 -Action install" -ForegroundColor White
        Write-Host "  .\install_windows_service.ps1 -Action start" -ForegroundColor White
        Write-Host "  .\install_windows_service.ps1 -Action status" -ForegroundColor White
        Write-Host "  .\install_windows_service.ps1 -Action remove" -ForegroundColor White
    }
}

Write-Host ""
Write-Host "注意事项:" -ForegroundColor Yellow
Write-Host "1. 确保MySQL和Redis服务已启动" -ForegroundColor White
Write-Host "2. 确保防火墙允许端口 $Port" -ForegroundColor White
Write-Host "3. 建议安装NSSM工具以获得更好的服务管理体验" -ForegroundColor White
Write-Host "   下载地址: https://nssm.cc/download" -ForegroundColor White

Read-Host "按任意键退出"
