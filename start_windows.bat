@echo off
REM Windows启动脚本 - DevOps运维平台
REM 作者: <PERSON>
REM 时间: 2025-01-01

echo ========================================
echo    DevOps运维平台 - Windows启动脚本
echo ========================================

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: Python未安装或未添加到PATH环境变量
    echo 请从 https://python.org 下载并安装Python 3.9+
    pause
    exit /b 1
)

REM 检查虚拟环境是否存在
if not exist "venv\Scripts\activate.bat" (
    echo 创建Python虚拟环境...
    python -m venv venv
    if errorlevel 1 (
        echo 错误: 创建虚拟环境失败
        pause
        exit /b 1
    )
)

REM 激活虚拟环境
echo 激活虚拟环境...
call venv\Scripts\activate.bat

REM 检查配置文件
if not exist "config.py" (
    if exist "config_windows.py.sample" (
        echo 复制Windows配置文件...
        copy config_windows.py.sample config.py
    ) else (
        echo 错误: 配置文件不存在，请先复制config_windows.py.sample为config.py并修改配置
        pause
        exit /b 1
    )
)

REM 检查依赖是否安装
echo 检查Python依赖...
pip show django >nul 2>&1
if errorlevel 1 (
    echo 安装Python依赖...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖安装失败
        pause
        exit /b 1
    )
)

REM 创建日志目录
if not exist "logs" mkdir logs

REM 检查数据库连接
echo 检查数据库连接...
python manage.py check --database default
if errorlevel 1 (
    echo 警告: 数据库连接检查失败，请确认MySQL服务已启动并配置正确
)

REM 运行数据库迁移
echo 运行数据库迁移...
python manage.py makemigrations
python manage.py migrate

REM 收集静态文件
echo 收集静态文件...
python manage.py collectstatic --noinput

REM 启动服务
echo ========================================
echo 启动DevOps运维平台服务...
echo 访问地址: http://localhost:8080
echo 按 Ctrl+C 停止服务
echo ========================================

REM 使用daphne启动ASGI应用
if exist "logs\daphne.log" del logs\daphne.log
daphne devops_backend.asgi:application -b 0.0.0.0 -p 8080 --access-log logs\daphne.log

pause
